import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/views/quiz_result.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:bibl/widgets/question_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'controllers/analytics_controller.dart';
import 'widgets/arrow_back_button_widget.dart';
import 'widgets/customappbar.dart';

class Quiz extends StatefulWidget {
  final QuizModel quiz;
  const Quiz({super.key, required this.quiz});

  @override
  State<Quiz> createState() => _QuizState();
}

class _QuizState extends State<Quiz> {
  ProfileController profileController = Get.find();
  final PageController _pageController = PageController();
  final AnalticsController analticsController = AnalticsController();
  int currentPage = 0;

  List<String?> selectedOptions = [];
  List<int?> selectedOptionsIndexes = [];
  bool _isTransitioning = false; // Track to prevent multiple taps

  @override
  void initState() {
    super.initState();
    analticsController.quizStartedAnalyticsUpdate(widget.quiz.quizName!);
    for (var qs in widget.quiz.questionsList!) {
      selectedOptions.add(null);
      selectedOptionsIndexes.add(null);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (profileController.userr.value.isPremiumUser! == false) {
        profileController.updatedOpenedQuizesAndArticles(widget.quiz.quizId!);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  bool _isGoingBack = false;
  void _goToPreviousPage() {
    if (_isGoingBack || _isTransitioning || !mounted) return;

    setState(() => _isTransitioning = true);
    if (currentPage > 0) {
      if (mounted) {
        setState(() {
          currentPage--;
          _pageController.jumpToPage(currentPage); // Instant jump
          _isTransitioning = false;
        });
      }
    } else {
      _isGoingBack = true;
      Get.back();
    }
  }

  void _goToNextPage() {
    if (_isTransitioning || !mounted) return;

    setState(() => _isTransitioning = true);
    if (currentPage < widget.quiz.questionsList!.length - 1) {
      if (mounted) {
        setState(() {
          currentPage++;
          _pageController.jumpToPage(currentPage); // Instant jump
          _isTransitioning = false;
        });
      }
    } else {
      Get.off(
          () => QuizResult(
                finishedQuizModel: widget.quiz,
                selectedAnswers: selectedOptions,
              ),
          transition: Transition.rightToLeft,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut);
      if (mounted) {
        setState(() => _isTransitioning = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final question = widget.quiz.questionsList![currentPage];

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        _goToPreviousPage();
      },
      child: SafeArea(
        bottom: true,
        top: false,
        child: Scaffold(
          appBar: quizAppbarWidget(),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: PageView.builder(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              onPageChanged: (page) {
                if (mounted) {
                  setState(() {
                    currentPage = page;
                  });
                }
              },
              itemCount: widget.quiz.questionsList!.length,
              itemBuilder: (context, index) {
                return Stack(
                  children: [
                    ScrollConfiguration(
                      behavior: const ScrollBehavior(),
                      child: ListView(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.black.withValues(alpha: 0.2),
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              padding: const EdgeInsets.all(16),
                              children: [
                                QuestionWidget(
                                  totalQuestions:
                                      widget.quiz.questionsList!.length,
                                  question: question,
                                  selectedOptionIndexx:
                                      selectedOptionsIndexes[currentPage],
                                  isAnswered:
                                      selectedOptions[currentPage] != null,
                                  onOptionSelected: (
                                      {required option,
                                      required selectedIndex}) {
                                    selectedOptions[currentPage] = option;
                                    selectedOptionsIndexes[currentPage] =
                                        selectedIndex;
                                    if (mounted) {
                                      setState(() {});
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 150),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: buttonContainer(
                        text: 'Potvrdi',
                        onTap: selectedOptions.length <= currentPage ||
                                selectedOptions[currentPage] == null ||
                                _isTransitioning
                            ? null
                            : () {
                                _goToNextPage();
                              },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  CustomAppBar quizAppbarWidget() {
    return CustomAppBar(
      widget: Row(
        children: [
          const SizedBox(width: 10),
          arrowBackButtonWidget(),
          const SizedBox(width: 10),
          Expanded(
            child: LinearProgressIndicator(
              borderRadius: BorderRadius.circular(16),
              minHeight: 6,
              value: (currentPage + 1) / widget.quiz.questionsList!.length,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            width: 60,
            height: 28,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('assets/svgs/cup_icon.svg'),
                const SizedBox(width: 5),
                const Txt(
                  txt: '10+',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontColor: Colors.white,
                ),
              ],
            ),
          ),
          const SizedBox(width: 30),
        ],
      ),
    );
  }
}
