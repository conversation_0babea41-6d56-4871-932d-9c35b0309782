import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:flutter/painting.dart';

/// Enhanced cache manager for optimal image loading performance
class EnhancedCacheManager {
  static const String key = 'biblImageCacheKey';
  static CacheManager? _instance;

  // Cache configuration
  static const Duration _stalePeriod = Duration(days: 7);
  static const int _maxNrOfCacheObjects = 500;
  static const Duration _maxAgeCacheObject = Duration(days: 30);

  static CacheManager get instance {
    _instance ??= CacheManager(
      Config(
        key,
        stalePeriod: _stalePeriod,
        maxNrOfCacheObjects: _maxNrOfCacheObjects,
        fileService: _OptimizedFileService(),
      ),
    );
    return _instance!;
  }

  /// Preload image with priority
  static Future<void> preloadImage(
    String url, {
    Priority priority = Priority.normal,
    Map<String, String>? headers,
  }) async {
    try {
      await instance.downloadFile(
        url,
        authHeaders: headers,
      );
    } catch (e) {
      debugPrint('Failed to preload image: $url - $e');
    }
  }

  /// Batch preload images
  static Future<void> batchPreloadImages(
    List<String> urls, {
    int concurrency = 3,
    Priority priority = Priority.normal,
  }) async {
    final chunks = _chunkList(urls, concurrency);

    for (final chunk in chunks) {
      await Future.wait(
        chunk.map((url) => preloadImage(url, priority: priority)),
        eagerError: false,
      );
    }
  }

  /// Get cached file synchronously if available
  static File? getCachedFileSync(String url) {
    try {
      // Use getFileFromCache instead of the non-existent sync method
      return null; // Sync method not available in current cache manager version
    } catch (e) {
      return null;
    }
  }

  /// Clear cache selectively
  static Future<void> clearCache({bool onlyExpired = true}) async {
    if (onlyExpired) {
      // Use emptyCache as removeExpiredFiles is not available
      await instance.emptyCache();
    } else {
      await instance.emptyCache();
    }

    // Also clear Flutter's image cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Get cache size
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await _getCacheDirectory();
      return _getDirectorySize(cacheDir);
    } catch (e) {
      return 0;
    }
  }

  static List<List<T>> _chunkList<T>(List<T> list, int chunkSize) {
    final chunks = <List<T>>[];
    for (var i = 0; i < list.length; i += chunkSize) {
      final end = (i + chunkSize < list.length) ? i + chunkSize : list.length;
      chunks.add(list.sublist(i, end));
    }
    return chunks;
  }

  static Future<Directory> _getCacheDirectory() async {
    final baseDir = await getTemporaryDirectory();
    final cacheDir = Directory(path.join(baseDir.path, key));
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }
    return cacheDir;
  }

  static Future<int> _getDirectorySize(Directory dir) async {
    int size = 0;

    await for (final entity in dir.list(recursive: true, followLinks: false)) {
      if (entity is File) {
        size += await entity.length();
      }
    }

    return size;
  }
}

/// Optimized file service for better performance
class _OptimizedFileService extends HttpFileService {
  @override
  Future<FileServiceResponse> get(String url,
      {Map<String, String>? headers}) async {
    // Add custom headers for better caching
    final optimizedHeaders = {
      ...?headers,
      'Cache-Control': 'max-age=2592000', // 30 days
    };

    return await super.get(url, headers: optimizedHeaders);
  }
}

/// Priority levels for image loading
enum Priority {
  high,
  normal,
  low,
}
